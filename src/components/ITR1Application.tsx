import React, { useState, useEffect, useCallback, useMemo } from 'react';

// Initial form state based on ITR-1 schema
const initialFormState = {
  creationInfo: {
    swVersionNo: '1.0',
    swCreatedBy: '**********',
    jsonCreatedBy: '**********',
    jsonCreationDate: new Date().toISOString().split('T')[0],
    intermediaryCity: 'Delhi',
    digest: ''
  },
  formITR1: {
    formName: 'ITR-1',
    description: 'ITR-1 for AY 2024-25',
    assessmentYear: '2024',
    schemaVer: 'Ver1.0',
    formVer: 'Ver1.0'
  },
  personalInfo: {
    assesseeName: {
      firstName: '',
      middleName: '',
      surNameOrOrgName: ''
    },
    pan: '',
    address: {
      residenceNo: '',
      residenceName: '',
      roadOrStreet: '',
      localityOrArea: '',
      cityOrTownOrDistrict: '',
      stateCode: '',
      countryCode: '91',
      pinCode: '',
      countryCodeMobile: '91',
      mobileNo: '',
      emailAddress: ''
    },
    dob: '',
    employerCategory: 'OTH',
    aadhaarCardNo: ''
  },
  filingStatus: {
    returnFileSec: 11,
    optOutNewTaxRegime: 'N',
    seventhProvisio139: 'N',
    itrFilingDueDate: '2024-07-31'
  },
  incomeDeductions: {
    grossSalary: 0,
    salary: 0,
    perquisitesValue: 0,
    profitsInSalary: 0,
    incomeNotified89A: 0,
    netSalary: 0,
    deductionUs16: 0,
    deductionUs16ia: 0,
    entertainmentAlw16ii: 0,
    professionalTaxUs16iii: 0,
    incomeFromSal: 0,
    typeOfHP: 'S',
    grossRentReceived: 0,
    taxPaidlocalAuth: 0,
    annualValue: 0,
    standardDeduction: 0,
    interestPayable: 0,
    totalIncomeOfHP: 0,
    incomeOthSrc: 0,
    deductionUs57iia: 0,
    grossTotIncome: 0,
    totalIncome: 0
  },
  usrDeductUndChapVIA: {
    section80C: 0,
    section80CCC: 0,
    section80CCDEmployeeOrSE: 0,
    section80CCD1B: 0,
    section80CCDEmployer: 0,
    section80D: 0,
    section80DD: 0,
    section80DDB: 0,
    section80E: 0,
    section80EE: 0,
    section80G: 0,
    section80GG: 0,
    section80GGA: 0,
    section80GGC: 0,
    section80U: 0,
    section80TTA: 0,
    section80TTB: 0,
    totalChapVIADeductions: 0
  },
  taxComputation: {
    totalTaxPayable: 0,
    rebate87A: 0,
    taxPayableOnRebate: 0,
    educationCess: 0,
    grossTaxLiability: 0,
    section89: 0,
    netTaxLiability: 0,
    totalIntrstPay: 0,
    totTaxPlusIntrstPay: 0
  },
  taxPaid: {
    taxesPaid: {
      advanceTax: 0,
      tds: 0,
      tcs: 0,
      selfAssessmentTax: 0,
      totalTaxesPaid: 0
    },
    balTaxPayable: 0
  },
  refund: {
    refundDue: 0,
    bankAccountDtls: {
      addtnlBankDetails: [{
        ifscCode: '',
        bankName: '',
        bankAccountNo: '',
        accountType: 'SB'
      }]
    }
  },
  schedules: {
    schedule80G: {
      totalDonationsUs80GCash: 0,
      totalDonationsUs80GOtherMode: 0,
      totalDonationsUs80G: 0,
      totalEligibleDonationsUs80G: 0,
      donations: []
    },
    schedule80D: {
      sec80DSelfFamSrCtznHealth: {
        seniorCitizenFlag: 'N',
        selfAndFamily: 0,
        healthInsPremSlfFam: 0,
        prevHlthChckUpSlfFam: 0,
        parentsSeniorCitizenFlag: 'N',
        parents: 0,
        healthInsPremParents: 0,
        prevHlthChckUpParents: 0,
        eligibleAmountOfDedn: 0
      }
    },
    tdsOnSalaries: {
      tdsOnSalary: [],
      totalTDSonSalaries: 0
    },
    tdsOnOthThanSals: {
      tdsOnOthThanSal: [],
      totalTDSonOthThanSals: 0
    }
  },
  verification: {
    declaration: {
      assesseeVerName: '',
      fatherName: '',
      assesseeVerPAN: ''
    },
    capacity: 'S',
    place: ''
  }
};

// Validation rules based on the document
const validationRules = {
  pan: /^[A-Z]{3}[P][A-Z][0-9]{4}[A-Z]$/,
  aadhaar: /^[0-9]{12}$/,
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  mobile: /^[1-9][0-9]{9}$/,
  pincode: /^[1-9][0-9]{5}$/,
  ifsc: /^[A-Z]{4}[0][A-Z0-9]{6}$/
};

// State codes for dropdown
const stateCodes = [
  { code: '01', name: 'Andaman and Nicobar Islands' },
  { code: '02', name: 'Andhra Pradesh' },
  { code: '03', name: 'Arunachal Pradesh' },
  { code: '04', name: 'Assam' },
  { code: '05', name: 'Bihar' },
  { code: '06', name: 'Chandigarh' },
  { code: '07', name: 'Dadra Nagar and Haveli' },
  { code: '08', name: 'Daman and Diu' },
  { code: '09', name: 'Delhi' },
  { code: '10', name: 'Goa' },
  { code: '11', name: 'Gujarat' },
  { code: '12', name: 'Haryana' },
  { code: '13', name: 'Himachal Pradesh' },
  { code: '14', name: 'Jammu and Kashmir' },
  { code: '15', name: 'Karnataka' },
  { code: '16', name: 'Kerala' },
  { code: '17', name: 'Lakshadweep' },
  { code: '18', name: 'Madhya Pradesh' },
  { code: '19', name: 'Maharashtra' },
  { code: '20', name: 'Manipur' },
  { code: '21', name: 'Meghalaya' },
  { code: '22', name: 'Mizoram' },
  { code: '23', name: 'Nagaland' },
  { code: '24', name: 'Odisha' },
  { code: '25', name: 'Puducherry' },
  { code: '26', name: 'Punjab' },
  { code: '27', name: 'Rajasthan' },
  { code: '28', name: 'Sikkim' },
  { code: '29', name: 'Tamil Nadu' },
  { code: '30', name: 'Tripura' },
  { code: '31', name: 'Uttar Pradesh' },
  { code: '32', name: 'West Bengal' },
  { code: '33', name: 'Chhattisgarh' },
  { code: '34', name: 'Uttarakhand' },
  { code: '35', name: 'Jharkhand' },
  { code: '36', name: 'Telangana' },
  { code: '37', name: 'Ladakh' }
];

// Employer categories
const employerCategories = [
  { code: 'CGOV', name: 'Central Government' },
  { code: 'SGOV', name: 'State Government' },
  { code: 'PSU', name: 'Public Sector Unit' },
  { code: 'PE', name: 'Pensioners - Central Government' },
  { code: 'PESG', name: 'Pensioners - State Government' },
  { code: 'PEPS', name: 'Pensioners - Public sector undertaking' },
  { code: 'PEO', name: 'Pensioners - Others' },
  { code: 'OTH', name: 'Others' },
  { code: 'NA', name: 'Not Applicable' }
];

// Fix 1: Add type for taxCalculations
interface TaxCalculations {
  totalTaxPayable?: number;
  rebate87A?: number;
  taxPayableOnRebate?: number;
  educationCess?: number;
  grossTaxLiability?: number;
  netTaxLiability?: number;
}

// Main ITR-1 Application Component
const ITR1Application = () => {
  const [formData, setFormData] = useState(initialFormState);
  const [activeStep, setActiveStep] = useState(0);
  const [validationErrors, setValidationErrors] = useState({});
  const [validationWarnings, setValidationWarnings] = useState({});
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [taxCalculations, setTaxCalculations] = useState<TaxCalculations>({});
  const [completedSections, setCompletedSections] = useState<Set<number>>(new Set());

  // Steps for the stepper
  const steps = [
    'Personal Information',
    'Filing Status',
    'Income Details',
    'Deductions',
    'Tax Computation',
    'Tax Paid & Refund',
    'Schedules',
    'Verification'
  ];

  // Add notification
  const addNotification = useCallback((message, type = 'info') => {
    const id = Date.now();
    setNotifications(prev => [...prev, { id, message, type }]);
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, 5000);
  }, []);

  const autoSave = useCallback(async () => {
    setIsAutoSaving(true);
    try {
      localStorage.setItem('itr1_draft', JSON.stringify(formData));
      addNotification('Draft saved automatically', 'success');
    } catch (error) {
      console.error('Auto-save failed:', error);
      addNotification('Auto-save failed', 'error');
    } finally {
      setIsAutoSaving(false);
    }
  }, [formData, addNotification]);

  // Auto-save functionality - only trigger when user stops typing
  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      if (!isAutoSaving) {
        autoSave();
      }
    }, 30000); // Auto-save every 30 seconds

    return () => clearTimeout(autoSaveTimer);
  }, [autoSave, isAutoSaving]);

  // Load saved data on component mount
  useEffect(() => {
    const savedData = localStorage.getItem('itr1_draft');
    if (savedData) {
      try {
        setFormData(JSON.parse(savedData));
        addNotification('Previous draft loaded', 'info');
      } catch (error) {
        console.error('Failed to load saved data:', error);
      }
    }
  }, [addNotification]);

  // Update form data with optimized state updates
  const updateFormData = useCallback((path, value) => {
    setFormData(prev => {
      const newData = { ...prev };
      const keys = path.split('.');
      let current = newData;

      // Handle nested object updates more efficiently
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        } else {
          current[keys[i]] = { ...current[keys[i]] };
        }
        current = current[keys[i]];
      }

      // Only update if value has actually changed
      if (current[keys[keys.length - 1]] !== value) {
        current[keys[keys.length - 1]] = value;
        return newData;
      }

      return prev; // Return previous state if no change
    });
  }, []);

  // Stable event handlers using useCallback with proper dependencies
  const handleInputChange = useCallback((e) => {
    const { name, value, type } = e.target;
    const processedValue = type === 'number' ? Number(value) || 0 : value;
    updateFormData(name, processedValue);
  }, [updateFormData]);

  const handleTextInputChange = useCallback((e) => {
    const { name, value } = e.target;
    const transform = e.target.dataset.transform;
    const processedValue = transform === 'uppercase' ? value.toUpperCase() : value;
    updateFormData(name, processedValue);
  }, [updateFormData]);

  const handleNumberInputChange = useCallback((e) => {
    const { name, value } = e.target;
    const min = Number(e.target.dataset.min) || 0;
    const max = Number(e.target.dataset.max) || Infinity;
    const numValue = Number(value) || 0;
    const processedValue = Math.min(max, Math.max(min, numValue));
    updateFormData(name, processedValue);
  }, [updateFormData]);

  const handleSelectChange = useCallback((e) => {
    const { name, value } = e.target;
    updateFormData(name, value);
  }, [updateFormData]);

  // Special handler for interest payable with conditional max limit
  const handleInterestPayableChange = useCallback((e) => {
    const value = Number(e.target.value);
    const maxLimit = formData.incomeDeductions.typeOfHP === 'S' ? 200000 : value;
    updateFormData('incomeDeductions.interestPayable', Math.min(maxLimit, value));
  }, [updateFormData, formData.incomeDeductions.typeOfHP]);

  // Calculate income totals using useMemo to prevent re-renders
  const calculatedValues = useMemo(() => {
    const income = formData.incomeDeductions;
    const deductions = formData.usrDeductUndChapVIA;
    
    // Calculate net salary
    const netSalary = Math.max(0, (income.grossSalary || 0) - (income.deductionUs16ia || 0) - (income.professionalTaxUs16iii || 0));
    
    // Calculate house property income
    let hpIncome = 0;
    let annualValue = 0;
    let standardDed = 0;
    
    if (income.typeOfHP !== 'S') {
      annualValue = Math.max(0, (income.grossRentReceived || 0) - (income.taxPaidlocalAuth || 0));
      standardDed = annualValue * 0.30;
      hpIncome = annualValue - standardDed - (income.interestPayable || 0);
    } else {
      hpIncome = -(Math.min(200000, income.interestPayable || 0));
    }
    
    // Calculate gross total income
    const grossTotal = netSalary + Math.max(-200000, hpIncome) + (income.incomeOthSrc || 0);
    
    // Calculate total deductions
    const totalDeductions = Object.keys(deductions)
      .filter(key => key !== 'totalChapVIADeductions')
      .reduce((sum, key) => sum + (deductions[key] || 0), 0);
    
    // Calculate total income
    const totalIncome = Math.max(0, grossTotal - totalDeductions);
    
    return {
      netSalary,
      incomeFromSal: netSalary,
      annualValue,
      standardDeduction: standardDed,
      totalIncomeOfHP: hpIncome,
      grossTotIncome: grossTotal,
      totalIncome,
      totalChapVIADeductions: totalDeductions
    };
  }, [
    formData.incomeDeductions.grossSalary, 
    formData.incomeDeductions.deductionUs16ia, 
    formData.incomeDeductions.professionalTaxUs16iii, 
    formData.incomeDeductions.typeOfHP,
    formData.incomeDeductions.grossRentReceived, 
    formData.incomeDeductions.interestPayable,
    formData.incomeDeductions.incomeOthSrc,
    formData.usrDeductUndChapVIA.section80C,
    formData.usrDeductUndChapVIA.section80CCC,
    formData.usrDeductUndChapVIA.section80CCDEmployeeOrSE,
    formData.usrDeductUndChapVIA.section80CCD1B,
    formData.usrDeductUndChapVIA.section80D,
    formData.usrDeductUndChapVIA.section80G,
    formData.usrDeductUndChapVIA.section80TTA
  ]);

  // Tax calculation using useMemo
  const calculatedTax = useMemo(() => {
    const { incomeDeductions, filingStatus } = formData;
    const isNewRegime = filingStatus.optOutNewTaxRegime === 'N';
    const totalIncome = calculatedValues.totalIncome || 0;
    
    let tax = 0;
    let rebate87A = 0;
    
    if (isNewRegime) {
      // New tax regime calculation
      if (totalIncome <= 300000) {
        tax = 0;
      } else if (totalIncome <= 600000) {
        tax = (totalIncome - 300000) * 0.05;
      } else if (totalIncome <= 900000) {
        tax = 15000 + (totalIncome - 600000) * 0.10;
      } else if (totalIncome <= 1200000) {
        tax = 45000 + (totalIncome - 900000) * 0.15;
      } else if (totalIncome <= 1500000) {
        tax = 90000 + (totalIncome - 1200000) * 0.20;
      } else {
        tax = 150000 + (totalIncome - 1500000) * 0.30;
      }
      
      // Rebate 87A for new regime
      if (totalIncome <= 700000) {
        rebate87A = Math.min(tax, 25000);
      }
    } else {
      // Old tax regime calculation
      if (totalIncome <= 250000) {
        tax = 0;
      } else if (totalIncome <= 500000) {
        tax = (totalIncome - 250000) * 0.05;
      } else if (totalIncome <= 1000000) {
        tax = 12500 + (totalIncome - 500000) * 0.20;
      } else {
        tax = 112500 + (totalIncome - 1000000) * 0.30;
      }
      
      // Rebate 87A for old regime
      if (totalIncome <= 500000) {
        rebate87A = Math.min(tax, 12500);
      }
    }
    
    const taxAfterRebate = Math.max(0, tax - rebate87A);
    const educationCess = taxAfterRebate * 0.04;
    const totalTaxLiability = taxAfterRebate + educationCess;
    
    return {
      totalTaxPayable: Math.round(tax),
      rebate87A: Math.round(rebate87A),
      taxPayableOnRebate: Math.round(taxAfterRebate),
      educationCess: Math.round(educationCess),
      grossTaxLiability: Math.round(totalTaxLiability),
      netTaxLiability: Math.round(totalTaxLiability)
    };
  }, [formData.filingStatus.optOutNewTaxRegime, calculatedValues.totalIncome]);

  // Update taxCalculations state when calculatedTax changes
  useEffect(() => {
    setTaxCalculations(calculatedTax);
  }, [calculatedTax]);

  // Validation functions
  const validateField = useCallback((fieldPath, value) => {
    const errors = [];
    const warnings = [];

    switch (fieldPath) {
      case 'personalInfo.pan':
        if (!validationRules.pan.test(value)) {
          errors.push('Invalid PAN format. Should be like **********');
        }
        break;
      
      case 'personalInfo.aadhaarCardNo':
        if (value && !validationRules.aadhaar.test(value)) {
          errors.push('Invalid Aadhaar format. Should be 12 digits');
        }
        break;
      
      case 'personalInfo.address.emailAddress':
        if (!validationRules.email.test(value)) {
          errors.push('Invalid email format');
        }
        break;
      
      case 'personalInfo.address.mobileNo':
        if (!validationRules.mobile.test(value)) {
          errors.push('Invalid mobile number format');
        }
        break;
      
      case 'personalInfo.address.pinCode':
        if (!validationRules.pincode.test(value)) {
          errors.push('Invalid PIN code format');
        }
        break;
      
      case 'incomeDeductions.totalIncome':
        if (value > 5000000) {
          errors.push('Total income should not be greater than Rs 50 lakhs for ITR-1');
        }
        break;
      
      default:
        break;
    }

    return { errors, warnings };
  }, []);

  // Comprehensive validation for entire form
  const validateForm = useCallback(() => {
    const errors = {};
    const warnings = {};

    // Category A validations (blocking errors)
    
    // Personal Info validations
    if (!formData.personalInfo.assesseeName.surNameOrOrgName) {
      errors['personalInfo.assesseeName.surNameOrOrgName'] = ['Surname is mandatory'];
    }
    
    if (!formData.personalInfo.pan) {
      errors['personalInfo.pan'] = ['PAN is mandatory'];
    } else if (!validationRules.pan.test(formData.personalInfo.pan)) {
      errors['personalInfo.pan'] = ['Invalid PAN format'];
    }
    
    if (!formData.personalInfo.dob) {
      errors['personalInfo.dob'] = ['Date of birth is mandatory'];
    }
    
    if (!formData.personalInfo.address.residenceNo) {
      errors['personalInfo.address.residenceNo'] = ['Residence number is mandatory'];
    }
    
    if (!formData.personalInfo.address.localityOrArea) {
      errors['personalInfo.address.localityOrArea'] = ['Locality/Area is mandatory'];
    }
    
    if (!formData.personalInfo.address.cityOrTownOrDistrict) {
      errors['personalInfo.address.cityOrTownOrDistrict'] = ['City/Town/District is mandatory'];
    }
    
    if (!formData.personalInfo.address.stateCode) {
      errors['personalInfo.address.stateCode'] = ['State is mandatory'];
    }
    
    if (!formData.personalInfo.address.pinCode) {
      errors['personalInfo.address.pinCode'] = ['PIN Code is mandatory'];
    } else if (!validationRules.pincode.test(formData.personalInfo.address.pinCode)) {
      errors['personalInfo.address.pinCode'] = ['Invalid PIN Code format'];
    }
    
    if (!formData.personalInfo.address.mobileNo) {
      errors['personalInfo.address.mobileNo'] = ['Mobile number is mandatory'];
    } else if (!validationRules.mobile.test(formData.personalInfo.address.mobileNo)) {
      errors['personalInfo.address.mobileNo'] = ['Invalid mobile number format'];
    }
    
    if (!formData.personalInfo.address.emailAddress) {
      errors['personalInfo.address.emailAddress'] = ['Email address is mandatory'];
    } else if (!validationRules.email.test(formData.personalInfo.address.emailAddress)) {
      errors['personalInfo.address.emailAddress'] = ['Invalid email format'];
    }

    // Income validations
    if (formData.incomeDeductions.totalIncome > 5000000) {
      errors['incomeDeductions.totalIncome'] = ['Total income exceeds Rs 50 lakhs limit for ITR-1'];
    }

    // House property validations
    if (formData.incomeDeductions.typeOfHP === 'S' && formData.incomeDeductions.interestPayable > 200000) {
      errors['incomeDeductions.interestPayable'] = ['Interest on housing loan for self-occupied property cannot exceed Rs 2,00,000'];
    }

    // Deduction validations for old regime
    if (formData.filingStatus.optOutNewTaxRegime === 'Y') {
      // Standard deduction validation
      if (formData.incomeDeductions.deductionUs16ia > 50000) {
        errors['incomeDeductions.deductionUs16ia'] = ['Standard deduction cannot exceed Rs 50,000'];
      }
      
      // Professional tax validation
      if (formData.incomeDeductions.professionalTaxUs16iii > 5000) {
        errors['incomeDeductions.professionalTaxUs16iii'] = ['Professional tax cannot exceed Rs 5,000'];
      }
      
      // 80C, 80CCC, 80CCD(1) combined limit
      const section80CCombined = (formData.usrDeductUndChapVIA.section80C || 0) + 
                                 (formData.usrDeductUndChapVIA.section80CCC || 0) + 
                                 (formData.usrDeductUndChapVIA.section80CCDEmployeeOrSE || 0);
      if (section80CCombined > 150000) {
        errors['usrDeductUndChapVIA.combined80C'] = ['Combined deduction u/s 80C, 80CCC & 80CCD(1) cannot exceed Rs 1,50,000'];
      }
      
      // 80CCD(1B) limit
      if (formData.usrDeductUndChapVIA.section80CCD1B > 50000) {
        errors['usrDeductUndChapVIA.section80CCD1B'] = ['Deduction u/s 80CCD(1B) cannot exceed Rs 50,000'];
      }
      
      // 80D limit
      if (formData.usrDeductUndChapVIA.section80D > 100000) {
        errors['usrDeductUndChapVIA.section80D'] = ['Deduction u/s 80D cannot exceed Rs 1,00,000'];
      }
    } else {
      // New regime validations - most deductions not allowed
      const notAllowedDeductions = [
        'section80C', 'section80CCC', 'section80CCDEmployeeOrSE', 'section80D', 
        'section80DD', 'section80DDB', 'section80E', 'section80EE', 'section80G', 
        'section80GG', 'section80GGA', 'section80GGC', 'section80U', 'section80TTA', 'section80TTB'
      ];
      
      notAllowedDeductions.forEach(deduction => {
        if (formData.usrDeductUndChapVIA[deduction] > 0) {
          errors[`usrDeductUndChapVIA.${deduction}`] = ['This deduction is not allowed in new tax regime'];
        }
      });
    }

    // Verification validations
    if (!formData.verification.declaration.assesseeVerName) {
      errors['verification.declaration.assesseeVerName'] = ['Assessee name for verification is mandatory'];
    }
    
    if (!formData.verification.declaration.fatherName) {
      errors['verification.declaration.fatherName'] = ['Father name is mandatory'];
    }
    
    if (!formData.verification.declaration.assesseeVerPAN) {
      errors['verification.declaration.assesseeVerPAN'] = ['PAN for verification is mandatory'];
    }
    
    if (!formData.verification.place) {
      errors['verification.place'] = ['Place is mandatory'];
    }

    // Category B validations (warnings)
    
    // Check if PAN matches verification PAN
    if (formData.personalInfo.pan && formData.verification.declaration.assesseeVerPAN && 
        formData.personalInfo.pan !== formData.verification.declaration.assesseeVerPAN) {
      warnings['verification.declaration.assesseeVerPAN'] = ['PAN does not match with personal info PAN'];
    }

    // Check if name matches verification name
    const fullName = `${formData.personalInfo.assesseeName.firstName} ${formData.personalInfo.assesseeName.middleName} ${formData.personalInfo.assesseeName.surNameOrOrgName}`.trim();
    if (fullName && formData.verification.declaration.assesseeVerName && 
        fullName.toLowerCase() !== formData.verification.declaration.assesseeVerName.toLowerCase()) {
      warnings['verification.declaration.assesseeVerName'] = ['Name does not match with personal info name'];
    }

    // Update validation state only when explicitly called
    setValidationErrors(errors);
    setValidationWarnings(warnings);
    
    return { errors, warnings };
  }, []); // Remove formData dependency

  // Generate JSON file
  const generateJSON = useCallback(() => {
    const validation = validateForm();
    
    if (Object.keys(validation.errors).length > 0) {
      addNotification('Please fix all validation errors before generating JSON', 'error');
      return;
    }

    const itrData = {
      ITR: {
        ITR1: {
          CreationInfo: formData.creationInfo,
          Form_ITR1: formData.formITR1,
          PersonalInfo: formData.personalInfo,
          FilingStatus: formData.filingStatus,
          ITR1_IncomeDeductions: formData.incomeDeductions,
          UsrDeductUndChapVIA: formData.usrDeductUndChapVIA,
          ITR1_TaxComputation: formData.taxComputation,
          TaxPaid: formData.taxPaid,
          Refund: formData.refund,
          ...formData.schedules,
          Verification: formData.verification
        }
      }
    };
    
    const jsonString = JSON.stringify(itrData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ITR1_AY2024-25_${formData.personalInfo.pan || 'DRAFT'}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    addNotification('JSON file downloaded successfully', 'success');
  }, [formData, validateForm, addNotification]);

  // Navigation functions
  const handleNext = () => {
    const validation = validateForm();
    if (Object.keys(validation.errors).length === 0) {
      setCompletedSections(prev => new Set([...prev, activeStep]));
      setActiveStep(prev => Math.min(prev + 1, steps.length - 1));
    } else {
      addNotification('Please fix validation errors before proceeding', 'error');
    }
  };

  const handleBack = () => {
    setActiveStep(prev => Math.max(prev - 1, 0));
  };

  const handleStepClick = (step) => {
    setActiveStep(step);
  };

  // Fix 2: Type guard for JSON.parse
  const loadDataFromFile = useCallback((event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const result = e.target?.result;
          const data = typeof result === 'string' ? JSON.parse(result) : {};
          if (data.ITR && data.ITR.ITR1) {
            setFormData(prev => ({ ...prev, ...data.ITR.ITR1 }));
            addNotification('Data loaded successfully', 'success');
          } else {
            addNotification('Invalid file format', 'error');
          }
        } catch (error) {
          addNotification('Error loading file', 'error');
        }
      };
      reader.readAsText(file);
    }
  }, [addNotification]);

  // Personal Information Component with memoized event handlers
  const PersonalInformationForm = useMemo(() => (
    <div className="form-section">
      <h2>Personal Information</h2>
      <div className="form-grid">
        <div className="form-group">
          <label htmlFor="firstName">First Name *</label>
          <input
            id="firstName"
            type="text"
            value={formData.personalInfo.assesseeName.firstName}
            onChange={handleInputChange}
            name="personalInfo.assesseeName.firstName"
            autoComplete="given-name"
            className={validationErrors['personalInfo.assesseeName.firstName'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.assesseeName.firstName'] && (
            <span className="error-text">{validationErrors['personalInfo.assesseeName.firstName'][0]}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="middleName">Middle Name</label>
          <input
            id="middleName"
            type="text"
            value={formData.personalInfo.assesseeName.middleName}
            onChange={handleInputChange}
            name="personalInfo.assesseeName.middleName"
            autoComplete="additional-name"
          />
        </div>

        <div className="form-group">
          <label htmlFor="lastName">Last Name / Surname *</label>
          <input
            id="lastName"
            type="text"
            value={formData.personalInfo.assesseeName.surNameOrOrgName}
            onChange={handleInputChange}
            name="personalInfo.assesseeName.surNameOrOrgName"
            autoComplete="family-name"
            className={validationErrors['personalInfo.assesseeName.surNameOrOrgName'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.assesseeName.surNameOrOrgName'] && (
            <span className="error-text">{validationErrors['personalInfo.assesseeName.surNameOrOrgName'][0]}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="pan">PAN *</label>
          <input
            id="pan"
            type="text"
            value={formData.personalInfo.pan}
            onChange={handleTextInputChange}
            name="personalInfo.pan"
            data-transform="uppercase"
            autoComplete="off"
            placeholder="**********"
            maxLength={10}
            className={validationErrors['personalInfo.pan'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.pan'] && (
            <span className="error-text">{validationErrors['personalInfo.pan'][0]}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="dob">Date of Birth *</label>
          <input
            id="dob"
            type="date"
            value={formData.personalInfo.dob}
            onChange={handleInputChange}
            name="personalInfo.dob"
            autoComplete="bday"
            className={validationErrors['personalInfo.dob'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.dob'] && (
            <span className="error-text">{validationErrors['personalInfo.dob'][0]}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="aadhaar">Aadhaar Number</label>
          <input
            id="aadhaar"
            type="text"
            value={formData.personalInfo.aadhaarCardNo}
            onChange={handleInputChange}
            name="personalInfo.aadhaarCardNo"
            autoComplete="off"
            placeholder="123456789012"
            maxLength={12}
            className={validationErrors['personalInfo.aadhaarCardNo'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.aadhaarCardNo'] && (
            <span className="error-text">{validationErrors['personalInfo.aadhaarCardNo'][0]}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="employerCategory">Employer Category</label>
          <select
            id="employerCategory"
            value={formData.personalInfo.employerCategory}
            onChange={handleInputChange}
            name="personalInfo.employerCategory"
            autoComplete="organization-title"
          >
            {employerCategories.map(cat => (
              <option key={cat.code} value={cat.code}>{cat.name}</option>
            ))}
          </select>
        </div>
      </div>

      <h3>Address Details</h3>
      <div className="form-grid">
        <div className="form-group">
          <label htmlFor="residenceNo">Residence No. *</label>
          <input
            id="residenceNo"
            type="text"
            value={formData.personalInfo.address.residenceNo}
            onChange={handleInputChange}
            name="personalInfo.address.residenceNo"
            autoComplete="address-line1"
            className={validationErrors['personalInfo.address.residenceNo'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.address.residenceNo'] && (
            <span className="error-text">{validationErrors['personalInfo.address.residenceNo'][0]}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="residenceName">Residence Name</label>
          <input
            id="residenceName"
            type="text"
            value={formData.personalInfo.address.residenceName}
            onChange={handleInputChange}
            name="personalInfo.address.residenceName"
            autoComplete="address-line2"
          />
        </div>

        <div className="form-group">
          <label htmlFor="roadOrStreet">Road or Street</label>
          <input
            id="roadOrStreet"
            type="text"
            value={formData.personalInfo.address.roadOrStreet}
            onChange={handleInputChange}
            name="personalInfo.address.roadOrStreet"
            autoComplete="address-line3"
          />
        </div>

        <div className="form-group">
          <label htmlFor="locality">Locality or Area *</label>
          <input
            id="locality"
            type="text"
            value={formData.personalInfo.address.localityOrArea}
            onChange={handleInputChange}
            name="personalInfo.address.localityOrArea"
            autoComplete="address-level2"
            className={validationErrors['personalInfo.address.localityOrArea'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.address.localityOrArea'] && (
            <span className="error-text">{validationErrors['personalInfo.address.localityOrArea'][0]}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="city">City/Town/District *</label>
          <input
            id="city"
            type="text"
            value={formData.personalInfo.address.cityOrTownOrDistrict}
            onChange={handleInputChange}
            name="personalInfo.address.cityOrTownOrDistrict"
            autoComplete="address-level1"
            className={validationErrors['personalInfo.address.cityOrTownOrDistrict'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.address.cityOrTownOrDistrict'] && (
            <span className="error-text">{validationErrors['personalInfo.address.cityOrTownOrDistrict'][0]}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="state">State *</label>
          <select
            id="state"
            value={formData.personalInfo.address.stateCode}
            onChange={handleInputChange}
            name="personalInfo.address.stateCode"
            autoComplete="address-level1"
            className={validationErrors['personalInfo.address.stateCode'] ? 'error' : ''}
          >
            <option value="">Select State</option>
            {stateCodes.map(state => (
              <option key={state.code} value={state.code}>{state.name}</option>
            ))}
          </select>
          {validationErrors['personalInfo.address.stateCode'] && (
            <span className="error-text">{validationErrors['personalInfo.address.stateCode'][0]}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="pinCode">PIN Code *</label>
          <input
            id="pinCode"
            type="text"
            value={formData.personalInfo.address.pinCode}
            onChange={handleInputChange}
            name="personalInfo.address.pinCode"
            autoComplete="postal-code"
            placeholder="123456"
            maxLength={6}
            className={validationErrors['personalInfo.address.pinCode'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.address.pinCode'] && (
            <span className="error-text">{validationErrors['personalInfo.address.pinCode'][0]}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="mobile">Mobile Number *</label>
          <input
            id="mobile"
            type="tel"
            value={formData.personalInfo.address.mobileNo}
            onChange={handleInputChange}
            name="personalInfo.address.mobileNo"
            autoComplete="tel"
            placeholder="9876543210"
            maxLength={10}
            className={validationErrors['personalInfo.address.mobileNo'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.address.mobileNo'] && (
            <span className="error-text">{validationErrors['personalInfo.address.mobileNo'][0]}</span>
          )}
        </div>

        <div className="form-group full-width">
          <label htmlFor="email">Email Address *</label>
          <input
            id="email"
            type="email"
            value={formData.personalInfo.address.emailAddress}
            onChange={handleInputChange}
            name="personalInfo.address.emailAddress"
            autoComplete="email"
            placeholder="<EMAIL>"
            className={validationErrors['personalInfo.address.emailAddress'] ? 'error' : ''}
          />
          {validationErrors['personalInfo.address.emailAddress'] && (
            <span className="error-text">{validationErrors['personalInfo.address.emailAddress'][0]}</span>
          )}
        </div>
      </div>
    </div>
  ), [formData.personalInfo, validationErrors, handleInputChange, handleTextInputChange, handleSelectChange]);

  // Filing Status Component
  const FilingStatusForm = useMemo(() => (
    <div className="form-section">
      <h2>Filing Status</h2>
      <div className="form-grid">
        <div className="form-group full-width">
          <label>Tax Regime Selection</label>
          <div className="radio-group">
            <label>
              <input
                type="radio"
                value="N"
                checked={formData.filingStatus.optOutNewTaxRegime === 'N'}
                onChange={handleInputChange} name="filingStatus.optOutNewTaxRegime"
              />
              New Tax Regime (Default)
            </label>
            <label>
              <input
                type="radio"
                value="Y"
                checked={formData.filingStatus.optOutNewTaxRegime === 'Y'}
                onChange={handleInputChange} name="filingStatus.optOutNewTaxRegime"
              />
              Old Tax Regime
            </label>
          </div>
          <div className="info-box">
            {formData.filingStatus.optOutNewTaxRegime === 'N' ? (
              <p>New tax regime: Lower tax rates but most deductions not available</p>
            ) : (
              <p>Old tax regime: Higher tax rates but deductions under Chapter VI-A available</p>
            )}
          </div>
        </div>
        
        <div className="form-group">
          <label>Return Filing Section</label>
          <select
            value={formData.filingStatus.returnFileSec}
            onChange={handleInputChange}
            data-path="filingStatus.returnFileSec"
          >
            <option value={11}>139(1) - On or before due date</option>
            <option value={12}>139(4) - After due date</option>
            <option value={17}>139(5) - Revised</option>
            <option value={21}>139(8A) - Updated</option>
          </select>
        </div>

        <div className="form-group">
          <label>ITR Filing Due Date</label>
          <input
            type="date"
            value={formData.filingStatus.itrFilingDueDate}
            onChange={handleInputChange} name="filingStatus.itrFilingDueDate"
            disabled
          />
        </div>
      </div>
    </div>
  ), [formData.filingStatus, handleSelectChange, handleInputChange]);

  // Income Details Component
  const IncomeDetailsForm = useMemo(() => (
    <div className="form-section">
      <h2>Income Details</h2>
      
      <div className="income-section">
        <h3>Salary Income</h3>
        <div className="form-grid">
          <div className="form-group">
            <label>Gross Salary</label>
            <input
              type="number"
              value={formData.incomeDeductions.grossSalary}
              onChange={handleInputChange} name="incomeDeductions.grossSalary"
              min="0"
            />
          </div>
          
          <div className="form-group">
            <label>Standard Deduction u/s 16(ia)</label>
            <input
              type="number"
              value={formData.incomeDeductions.deductionUs16ia}
              onChange={handleNumberInputChange} name="incomeDeductions.deductionUs16ia" data-min="0" data-max="50000"
              min="0"
              max="50000"
            />
            <span className="help-text">Maximum Rs. 50,000</span>
          </div>
          
          <div className="form-group">
            <label>Professional Tax u/s 16(iii)</label>
            <input
              type="number"
              value={formData.incomeDeductions.professionalTaxUs16iii}
              onChange={handleNumberInputChange} name="incomeDeductions.professionalTaxUs16iii" data-min="0" data-max="5000"
              min="0"
              max="5000"
            />
            <span className="help-text">Maximum Rs. 5,000</span>
          </div>
          
          <div className="form-group calculated">
            <label>Income from Salary</label>
            <input
              type="number"
              value={calculatedValues.incomeFromSal}
              readOnly
            />
            <span className="help-text">Auto-calculated</span>
          </div>
        </div>
      </div>
      
      <div className="income-section">
        <h3>House Property Income</h3>
        <div className="form-grid">
          <div className="form-group">
            <label>Type of House Property</label>
            <select
              value={formData.incomeDeductions.typeOfHP}
              onChange={handleInputChange} name="incomeDeductions.typeOfHP"
            >
              <option value="S">Self Occupied</option>
              <option value="L">Let Out</option>
              <option value="D">Deemed Let Out</option>
            </select>
          </div>
          
          {formData.incomeDeductions.typeOfHP !== 'S' && (
            <>
              <div className="form-group">
                <label>Gross Rent Received</label>
                <input
                  type="number"
                  value={formData.incomeDeductions.grossRentReceived}
                  onChange={handleInputChange} name="incomeDeductions.grossRentReceived"
                  min="0"
                />
              </div>

              <div className="form-group">
                <label>Tax Paid to Local Authority</label>
                <input
                  type="number"
                  value={formData.incomeDeductions.taxPaidlocalAuth}
                  onChange={handleInputChange} name="incomeDeductions.taxPaidlocalAuth"
                  min="0"
                />
              </div>
            </>
          )}
          
          <div className="form-group">
            <label>Interest on Housing Loan</label>
            <input
              type="number"
              value={formData.incomeDeductions.interestPayable}
              onChange={handleInterestPayableChange}
              min="0"
              className={validationErrors['incomeDeductions.interestPayable'] ? 'error' : ''}
            />
            <span className="help-text">
              {formData.incomeDeductions.typeOfHP === 'S' 
                ? "Maximum Rs. 2,00,000 for self-occupied" 
                : "No limit for let-out property"}
            </span>
            {validationErrors['incomeDeductions.interestPayable'] && (
              <span className="error-text">{validationErrors['incomeDeductions.interestPayable'][0]}</span>
            )}
          </div>
          
          <div className="form-group calculated">
            <label>Income from House Property</label>
            <input
              type="number"
              value={calculatedValues.totalIncomeOfHP}
              readOnly
            />
            <span className="help-text">Auto-calculated (can be negative up to Rs. 2,00,000)</span>
          </div>
        </div>
      </div>
      
      <div className="income-section">
        <h3>Income from Other Sources</h3>
        <div className="form-grid">
          <div className="form-group">
            <label>Interest from Savings Account</label>
            <input
              type="number"
              value={formData.incomeDeductions.incomeOthSrc}
              onChange={handleInputChange} name="incomeDeductions.incomeOthSrc"
              min="0"
            />
          </div>

          <div className="form-group">
            <label>Deduction u/s 57(iia) - Family Pension</label>
            <input
              type="number"
              value={formData.incomeDeductions.deductionUs57iia}
              onChange={handleNumberInputChange} name="incomeDeductions.deductionUs57iia" data-min="0" data-max="15000"
              min="0"
              max="15000"
            />
            <span className="help-text">Maximum Rs. 15,000</span>
          </div>
        </div>
      </div>
      
      <div className="income-section">
        <h3>Total Income Summary</h3>
        <div className="form-grid">
          <div className="form-group calculated">
            <label>Gross Total Income</label>
            <input
              type="number"
              value={calculatedValues.grossTotIncome}
              readOnly
            />
            <span className="help-text">Auto-calculated</span>
          </div>
          
          <div className="form-group calculated">
            <label>Total Deductions (Chapter VI-A)</label>
            <input
              type="number"
              value={calculatedValues.totalChapVIADeductions}
              readOnly
            />
            <span className="help-text">From deductions section</span>
          </div>
          
          <div className="form-group calculated">
            <label>Total Income</label>
            <input
              type="number"
              value={calculatedValues.totalIncome}
              readOnly
              className={calculatedValues.totalIncome > 5000000 ? 'error' : ''}
            />
            <span className="help-text">Auto-calculated</span>
            {calculatedValues.totalIncome > 5000000 && (
              <span className="error-text">Total income exceeds Rs. 50 lakhs. ITR-1 is not applicable.</span>
            )}
          </div>
        </div>
      </div>
    </div>
  ), [formData.incomeDeductions, validationErrors, handleInputChange, handleNumberInputChange, handleSelectChange, handleInterestPayableChange]);

  // Deductions Component
  const DeductionsForm = useMemo(() => (
    <div className="form-section">
      <h2>Deductions under Chapter VI-A</h2>
      
      {formData.filingStatus.optOutNewTaxRegime === 'N' && (
        <div className="warning-box">
          <strong>New Tax Regime Selected:</strong> Most deductions under Chapter VI-A are not available. 
          Only 80CCD(2) and 80CCH are allowed.
        </div>
      )}
      
      <div className="deductions-grid">
        <div className="form-group">
          <label>Section 80C</label>
          <input
            type="number"
            value={formData.usrDeductUndChapVIA.section80C}
            onChange={handleInputChange} name="usrDeductUndChapVIA.section80C"
            min="0"
            max="150000"
            disabled={formData.filingStatus.optOutNewTaxRegime === 'N'}
            className={validationErrors['usrDeductUndChapVIA.section80C'] ? 'error' : ''}
          />
          <span className="help-text">PPF, ELSS, Life Insurance, etc. (Max: Rs. 1,50,000)</span>
          {validationErrors['usrDeductUndChapVIA.section80C'] && (
            <span className="error-text">{validationErrors['usrDeductUndChapVIA.section80C'][0]}</span>
          )}
        </div>
        
        <div className="form-group">
          <label>Section 80CCC</label>
          <input
            type="number"
            value={formData.usrDeductUndChapVIA.section80CCC}
            onChange={handleInputChange} name="usrDeductUndChapVIA.section80CCC"
            min="0"
            max="150000"
            disabled={formData.filingStatus.optOutNewTaxRegime === 'N'}
            className={validationErrors['usrDeductUndChapVIA.section80CCC'] ? 'error' : ''}
          />
          <span className="help-text">Pension Fund (Max: Rs. 1,50,000)</span>
          {validationErrors['usrDeductUndChapVIA.section80CCC'] && (
            <span className="error-text">{validationErrors['usrDeductUndChapVIA.section80CCC'][0]}</span>
          )}
        </div>
        
        <div className="form-group">
          <label>Section 80CCD(1)</label>
          <input
            type="number"
            value={formData.usrDeductUndChapVIA.section80CCDEmployeeOrSE}
            onChange={handleInputChange} name="usrDeductUndChapVIA.section80CCDEmployeeOrSE"
            min="0"
            max="150000"
            disabled={formData.filingStatus.optOutNewTaxRegime === 'N'}
            className={validationErrors['usrDeductUndChapVIA.section80CCDEmployeeOrSE'] ? 'error' : ''}
          />
          <span className="help-text">NPS Employee Contribution (Max: Rs. 1,50,000)</span>
          {validationErrors['usrDeductUndChapVIA.section80CCDEmployeeOrSE'] && (
            <span className="error-text">{validationErrors['usrDeductUndChapVIA.section80CCDEmployeeOrSE'][0]}</span>
          )}
        </div>
        
        <div className="form-group">
          <label>Section 80CCD(1B)</label>
          <input
            type="number"
            value={formData.usrDeductUndChapVIA.section80CCD1B}
            onChange={handleNumberInputChange} name="usrDeductUndChapVIA.section80CCD1B" data-min="0" data-max="50000"
            min="0"
            max="50000"
            disabled={formData.filingStatus.optOutNewTaxRegime === 'N'}
            className={validationErrors['usrDeductUndChapVIA.section80CCD1B'] ? 'error' : ''}
          />
          <span className="help-text">Additional NPS (Max: Rs. 50,000)</span>
          {validationErrors['usrDeductUndChapVIA.section80CCD1B'] && (
            <span className="error-text">{validationErrors['usrDeductUndChapVIA.section80CCD1B'][0]}</span>
          )}
        </div>
        
        <div className="form-group">
          <label>Section 80CCD(2)</label>
          <input
            type="number"
            value={formData.usrDeductUndChapVIA.section80CCDEmployer}
            onChange={handleInputChange} name="usrDeductUndChapVIA.section80CCDEmployer"
            min="0"
          />
          <span className="help-text">NPS Employer Contribution</span>
        </div>
        
        <div className="form-group">
          <label>Section 80D</label>
          <input
            type="number"
            value={formData.usrDeductUndChapVIA.section80D}
            onChange={handleNumberInputChange} name="usrDeductUndChapVIA.section80D" data-min="0" data-max="100000"
            min="0"
            max="100000"
            disabled={formData.filingStatus.optOutNewTaxRegime === 'N'}
            className={validationErrors['usrDeductUndChapVIA.section80D'] ? 'error' : ''}
          />
          <span className="help-text">Medical Insurance (Max: Rs. 1,00,000)</span>
          {validationErrors['usrDeductUndChapVIA.section80D'] && (
            <span className="error-text">{validationErrors['usrDeductUndChapVIA.section80D'][0]}</span>
          )}
        </div>
        
        <div className="form-group">
          <label>Section 80G</label>
          <input
            type="number"
            value={formData.usrDeductUndChapVIA.section80G}
            onChange={handleInputChange} name="usrDeductUndChapVIA.section80G"
            min="0"
            disabled={formData.filingStatus.optOutNewTaxRegime === 'N'}
            className={validationErrors['usrDeductUndChapVIA.section80G'] ? 'error' : ''}
          />
          <span className="help-text">Donations</span>
          {validationErrors['usrDeductUndChapVIA.section80G'] && (
            <span className="error-text">{validationErrors['usrDeductUndChapVIA.section80G'][0]}</span>
          )}
        </div>
        
        <div className="form-group">
          <label>Section 80TTA</label>
          <input
            type="number"
            value={formData.usrDeductUndChapVIA.section80TTA}
            onChange={handleNumberInputChange} name="usrDeductUndChapVIA.section80TTA" data-min="0" data-max="10000"
            min="0"
            max="10000"
            disabled={formData.filingStatus.optOutNewTaxRegime === 'N'}
            className={validationErrors['usrDeductUndChapVIA.section80TTA'] ? 'error' : ''}
          />
          <span className="help-text">Savings Account Interest (Max: Rs. 10,000)</span>
          {validationErrors['usrDeductUndChapVIA.section80TTA'] && (
            <span className="error-text">{validationErrors['usrDeductUndChapVIA.section80TTA'][0]}</span>
          )}
        </div>
        
        <div className="form-group calculated full-width">
          <label>Total Chapter VI-A Deductions</label>
          <input
            type="number"
            value={formData.usrDeductUndChapVIA.totalChapVIADeductions}
            readOnly
          />
          <span className="help-text">Auto-calculated</span>
        </div>
        
        {validationErrors['usrDeductUndChapVIA.combined80C'] && (
          <div className="error-box full-width">
            {validationErrors['usrDeductUndChapVIA.combined80C'][0]}
          </div>
        )}
      </div>
    </div>
  ), [formData.usrDeductUndChapVIA, formData.filingStatus.optOutNewTaxRegime, validationErrors, handleInputChange, handleNumberInputChange]);

  // Tax Computation Component
  const TaxComputationForm = useMemo(() => (
    <div className="form-section">
      <div className="form-section-header">
        <h2>Tax Computation</h2>
        <p>Tax calculation based on your income and deductions</p>
      </div>
      
      <div className="tax-computation-grid">
        <div className="form-group calculated">
          <label>Tax on Total Income</label>
          <input
            type="number"
            value={taxCalculations.totalTaxPayable || 0}
            readOnly
          />
        </div>
        
        <div className="form-group calculated">
          <label>Rebate u/s 87A</label>
          <input
            type="number"
            value={taxCalculations.rebate87A || 0}
            readOnly
          />
          <span className="help-text">
            {formData.filingStatus.optOutNewTaxRegime === 'N' ? 'Up to Rs. 25,000' : 'Up to Rs. 12,500'}
          </span>
        </div>
        
        <div className="form-group calculated">
          <label>Tax After Rebate</label>
          <input
            type="number"
            value={taxCalculations.taxPayableOnRebate || 0}
            readOnly
          />
        </div>
        
        <div className="form-group calculated">
          <label>Health & Education Cess (4%)</label>
          <input
            type="number"
            value={taxCalculations.educationCess || 0}
            readOnly
          />
        </div>
        
        <div className="form-group calculated highlighted">
          <label>Total Tax Liability</label>
          <input
            type="number"
            value={taxCalculations.grossTaxLiability || 0}
            readOnly
          />
        </div>
        
        <div className="form-group calculated highlighted">
          <label>Net Tax Liability</label>
          <input
            type="number"
            value={taxCalculations.netTaxLiability || 0}
            readOnly
          />
        </div>
      </div>
      
      {formData.filingStatus.optOutNewTaxRegime === 'N' && formData.incomeDeductions.totalIncome > 0 && (
        <div className="tax-comparison">
          <h3>Tax Comparison</h3>
          <p>Consider comparing both tax regimes to optimize your tax liability.</p>
        </div>
      )}
    </div>
  ), [calculatedTax, formData.filingStatus.optOutNewTaxRegime]);

  // Bank Details Component
  const BankDetailsForm = useMemo(() => (
    <div className="form-section">
      <h2>Bank Details for Refund</h2>
      <div className="form-grid">
        <div className="form-group">
          <label>IFSC Code</label>
          <input
            type="text"
            value={formData.refund.bankAccountDtls.addtnlBankDetails[0]?.ifscCode || ''}
            onChange={handleTextInputChange}
            name="refund.bankAccountDtls.addtnlBankDetails.0.ifscCode"
            data-transform="uppercase"
            autoComplete="off"
            placeholder="SBIN0000123"
            maxLength={11}
          />
        </div>
        
        <div className="form-group">
          <label>Bank Name</label>
          <input
            type="text"
            value={formData.refund.bankAccountDtls.addtnlBankDetails[0]?.bankName || ''}
            onChange={handleInputChange}
            name="refund.bankAccountDtls.addtnlBankDetails.0.bankName"
            autoComplete="off"
          />
        </div>
        
        <div className="form-group">
          <label>Account Number</label>
          <input
            type="text"
            value={formData.refund.bankAccountDtls.addtnlBankDetails[0]?.bankAccountNo || ''}
            onChange={handleInputChange}
            name="refund.bankAccountDtls.addtnlBankDetails.0.bankAccountNo"
            autoComplete="off"
          />
        </div>
        
        <div className="form-group">
          <label>Account Type</label>
          <select
            value={formData.refund.bankAccountDtls.addtnlBankDetails[0]?.accountType || 'SB'}
            onChange={handleInputChange} name="refund.bankAccountDtls.addtnlBankDetails.0.accountType"
          >
            <option value="SB">Savings Account</option>
            <option value="CA">Current Account</option>
            <option value="CC">Cash Credit Account</option>
            <option value="OD">Over Draft Account</option>
            <option value="NRO">Non Resident Account</option>
            <option value="OTH">Other</option>
          </select>
        </div>
      </div>
    </div>
  ), [formData.refund.bankAccountDtls, handleTextInputChange, handleInputChange, handleSelectChange]);

  // Verification Component
  const VerificationForm = useMemo(() => (
    <div className="form-section">
      <h2>Verification</h2>
      <div className="form-grid">
        <div className="form-group">
          <label>Assessee Name for Verification *</label>
          <input
            type="text"
            value={formData.verification.declaration.assesseeVerName}
            onChange={handleInputChange} name="verification.declaration.assesseeVerName"
            className={validationErrors['verification.declaration.assesseeVerName'] ? 'error' : ''}
          />
          {validationErrors['verification.declaration.assesseeVerName'] && (
            <span className="error-text">{validationErrors['verification.declaration.assesseeVerName'][0]}</span>
          )}
          {validationWarnings['verification.declaration.assesseeVerName'] && (
            <span className="warning-text">{validationWarnings['verification.declaration.assesseeVerName'][0]}</span>
          )}
        </div>
        
        <div className="form-group">
          <label>Father's Name *</label>
          <input
            type="text"
            value={formData.verification.declaration.fatherName}
            onChange={handleInputChange} name="verification.declaration.fatherName"
            className={validationErrors['verification.declaration.fatherName'] ? 'error' : ''}
          />
          {validationErrors['verification.declaration.fatherName'] && (
            <span className="error-text">{validationErrors['verification.declaration.fatherName'][0]}</span>
          )}
        </div>
        
        <div className="form-group">
          <label>PAN for Verification *</label>
          <input
            type="text"
            value={formData.verification.declaration.assesseeVerPAN}
            onChange={handleTextInputChange} name="verification.declaration.assesseeVerPAN" data-transform="uppercase"
            maxLength={10}
            className={validationErrors['verification.declaration.assesseeVerPAN'] ? 'error' : ''}
          />
          {validationErrors['verification.declaration.assesseeVerPAN'] && (
            <span className="error-text">{validationErrors['verification.declaration.assesseeVerPAN'][0]}</span>
          )}
          {validationWarnings['verification.declaration.assesseeVerPAN'] && (
            <span className="warning-text">{validationWarnings['verification.declaration.assesseeVerPAN'][0]}</span>
          )}
        </div>
        
        <div className="form-group">
          <label>Place *</label>
          <input
            type="text"
            value={formData.verification.place}
            onChange={handleInputChange} name="verification.place"
            className={validationErrors['verification.place'] ? 'error' : ''}
          />
          {validationErrors['verification.place'] && (
            <span className="error-text">{validationErrors['verification.place'][0]}</span>
          )}
        </div>
        
        <div className="form-group full-width">
          <label>Capacity</label>
          <div className="radio-group">
            <label>
              <input
                type="radio"
                value="S"
                checked={formData.verification.capacity === 'S'}
                onChange={handleInputChange} name="verification.capacity"
              />
              Self
            </label>
            <label>
              <input
                type="radio"
                value="R"
                checked={formData.verification.capacity === 'R'}
                onChange={handleInputChange} name="verification.capacity"
              />
              Representative
            </label>
          </div>
        </div>
      </div>
    </div>
  ), [formData.verification, validationErrors, handleTextInputChange, handleSelectChange]);

  // Main render function
  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return PersonalInformationForm;
      case 1:
        return FilingStatusForm;
      case 2:
        return IncomeDetailsForm;
      case 3:
        return DeductionsForm;
      case 4:
        return TaxComputationForm;
      case 5:
        return BankDetailsForm;
      case 6:
        return (
          <div className="form-section">
            <h2>Schedules</h2>
            <p>Additional schedules (TDS, TCS, etc.) will be implemented here.</p>
          </div>
        );
      case 7:
        return VerificationForm;
      default:
        return null;
    }
  };

  return (
    <div className="itr-container">
      <style>{`
        .itr-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          background-color: #f5f5f5;
          min-height: 100vh;
        }

        .header {
          text-align: center;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 30px;
          border-radius: 10px;
          margin-bottom: 30px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
          margin: 0;
          font-size: 2.5em;
          font-weight: 300;
        }

        .header p {
          margin: 10px 0 0 0;
          opacity: 0.9;
        }

        .stepper {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
          background: white;
          padding: 20px;
          border-radius: 10px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          overflow-x: auto;
        }

        .step {
          flex: 1;
          text-align: center;
          position: relative;
          min-width: 120px;
          cursor: pointer;
          padding: 10px;
          border-radius: 5px;
          transition: all 0.3s ease;
        }

        .step:hover {
          background-color: #f0f0f0;
        }

        .step.active {
          background-color: #667eea;
          color: white;
        }

        .step.completed {
          background-color: #4caf50;
          color: white;
        }

        .step-number {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          background-color: #ddd;
          color: #666;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 10px auto;
          font-weight: bold;
        }

        .step.active .step-number {
          background-color: white;
          color: #667eea;
        }

        .step.completed .step-number {
          background-color: white;
          color: #4caf50;
        }

        .step-label {
          font-size: 0.9em;
          font-weight: 500;
        }

        .progress-bar {
          height: 4px;
          background-color: #e0e0e0;
          border-radius: 2px;
          margin: 20px 0;
          overflow: hidden;
        }

        .progress {
          height: 100%;
          background: linear-gradient(90deg, #667eea, #764ba2);
          border-radius: 2px;
          transition: width 0.3s ease;
        }

        .form-container {
          background: white;
          padding: 30px;
          border-radius: 10px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          margin-bottom: 30px;
        }

        .form-section h2 {
          color: #333;
          border-bottom: 2px solid #667eea;
          padding-bottom: 10px;
          margin-bottom: 25px;
        }

        .form-section h3 {
          color: #555;
          margin: 25px 0 15px 0;
          font-size: 1.2em;
        }

        .form-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
          margin-bottom: 20px;
        }

        .deductions-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 15px;
          margin-bottom: 20px;
        }

        .tax-computation-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
          margin-bottom: 20px;
        }

        .form-group {
          display: flex;
          flex-direction: column;
        }

        .form-group.full-width {
          grid-column: 1 / -1;
        }

        .form-group.calculated {
          background-color: #f8f9fa;
          padding: 15px;
          border-radius: 5px;
          border-left: 4px solid #28a745;
        }

        .form-group.highlighted {
          background-color: #fff3cd;
          border-left: 4px solid #ffc107;
        }

        .form-group label {
          font-weight: 600;
          margin-bottom: 5px;
          color: #333;
        }

        .form-group input,
        .form-group select {
          padding: 12px;
          border: 2px solid #ddd;
          border-radius: 5px;
          font-size: 16px;
          transition: border-color 0.3s ease;
          background-color: white;
        }

        .form-group input:focus,
        .form-group select:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group input:disabled {
          background-color: #f5f5f5;
          color: #666;
        }

        .form-group input.error,
        .form-group select.error {
          border-color: #dc3545;
          background-color: #fff5f5;
        }

        .help-text {
          font-size: 0.85em;
          color: #666;
          margin-top: 5px;
        }

        .error-text {
          font-size: 0.85em;
          color: #dc3545;
          margin-top: 5px;
          font-weight: 500;
        }

        .warning-text {
          font-size: 0.85em;
          color: #ff9800;
          margin-top: 5px;
          font-weight: 500;
        }

        .radio-group {
          display: flex;
          gap: 20px;
          margin-top: 10px;
        }

        .radio-group label {
          display: flex;
          align-items: center;
          font-weight: normal;
          cursor: pointer;
        }

        .radio-group input[type="radio"] {
          margin-right: 8px;
        }

        .info-box {
          background-color: #e3f2fd;
          border: 1px solid #2196f3;
          border-radius: 5px;
          padding: 15px;
          margin: 15px 0;
        }

        .warning-box {
          background-color: #fff3e0;
          border: 1px solid #ff9800;
          border-radius: 5px;
          padding: 15px;
          margin: 15px 0;
          color: #e65100;
        }

        .error-box {
          background-color: #ffebee;
          border: 1px solid #f44336;
          border-radius: 5px;
          padding: 15px;
          margin: 15px 0;
          color: #c62828;
        }

        .income-section {
          background-color: #fafafa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 25px;
          border-left: 4px solid #667eea;
        }

        .navigation {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: white;
          padding: 20px;
          border-radius: 10px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          margin-top: 20px;
        }

        .nav-left {
          display: flex;
          gap: 15px;
        }

        .nav-right {
          display: flex;
          gap: 15px;
        }

        .btn {
          padding: 12px 24px;
          border: none;
          border-radius: 5px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          gap: 8px;
        }

        .btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }

        .btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }

        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }

        .btn-success {
          background-color: #28a745;
          color: white;
        }

        .btn-outline {
          background-color: transparent;
          border: 2px solid #667eea;
          color: #667eea;
        }

        .notifications {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 1000;
          max-width: 400px;
        }

        .notification {
          background: white;
          border-radius: 5px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          margin-bottom: 10px;
          padding: 15px;
          border-left: 4px solid;
          animation: slideIn 0.3s ease;
        }

        .notification.success {
          border-left-color: #28a745;
        }

        .notification.error {
          border-left-color: #dc3545;
        }

        .notification.info {
          border-left-color: #17a2b8;
        }

        .notification.warning {
          border-left-color: #ffc107;
        }

        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }

        .floating-help {
          position: fixed;
          bottom: 20px;
          right: 20px;
          background: #667eea;
          color: white;
          border: none;
          border-radius: 50%;
          width: 56px;
          height: 56px;
          font-size: 24px;
          cursor: pointer;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;
        }

        .floating-help:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }

        .auto-save-indicator {
          position: fixed;
          top: 10px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 14px;
          z-index: 1000;
        }

        .tax-comparison {
          background-color: #f0f8ff;
          border: 1px solid #4a90e2;
          border-radius: 8px;
          padding: 20px;
          margin-top: 20px;
        }

        @media (max-width: 768px) {
          .itr-container {
            padding: 10px;
          }

          .header h1 {
            font-size: 1.8em;
          }

          .stepper {
            padding: 10px;
          }

          .step {
            min-width: 80px;
          }

          .step-label {
            font-size: 0.8em;
          }

          .form-container {
            padding: 20px;
          }

          .form-grid {
            grid-template-columns: 1fr;
          }

          .deductions-grid {
            grid-template-columns: 1fr;
          }

          .tax-computation-grid {
            grid-template-columns: 1fr;
          }

          .navigation {
            flex-direction: column;
            gap: 15px;
          }

          .nav-left,
          .nav-right {
            width: 100%;
            justify-content: center;
          }
        }
      `}</style>

      <div className="header">
        <h1>ITR-1 (Sahaj)</h1>
        <p>Assessment Year 2024-25 | Income Tax Return</p>
      </div>

      <div className="stepper">
        {steps.map((label, index) => (
          <div
            key={label}
            className={`step ${index === activeStep ? 'active' : ''} ${
              completedSections.has(index) ? 'completed' : ''
            }`}
            onClick={() => handleStepClick(index)}
          >
            <div className="step-number">
              {completedSections.has(index) ? '✓' : index + 1}
            </div>
            <div className="step-label">{label}</div>
          </div>
        ))}
      </div>

      <div className="progress-bar">
        <div 
          className="progress" 
          style={{ width: `${((activeStep + 1) / steps.length) * 100}%` }}
        />
      </div>

      {isAutoSaving && (
        <div className="auto-save-indicator">
          💾 Auto-saving...
        </div>
      )}

      <div className="form-container">
        {renderStepContent(activeStep)}
      </div>

      <div className="navigation">
        <div className="nav-left">
          <button
            className="btn btn-outline"
            onClick={handleBack}
            disabled={activeStep === 0}
          >
            ← Back
          </button>
        </div>

        <div className="nav-right">
          <input
            type="file"
            accept=".json"
            onChange={loadDataFromFile}
            style={{ display: 'none' }}
            id="fileInput"
          />
          <label htmlFor="fileInput" className="btn btn-secondary">
            📁 Load Data
          </label>

          <button
            className="btn btn-secondary"
            onClick={autoSave}
          >
            💾 Save Draft
          </button>

          {activeStep === steps.length - 1 ? (
            <button
              className="btn btn-success"
              onClick={generateJSON}
            >
              📥 Download JSON
            </button>
          ) : (
            <button
              className="btn btn-primary"
              onClick={handleNext}
            >
              Next →
            </button>
          )}
        </div>
      </div>

      <button className="floating-help" title="Help">
        ❓
      </button>

      <div className="notifications">
        {notifications.map(notification => (
          <div key={notification.id} className={`notification ${notification.type}`}>
            {notification.message}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ITR1Application;